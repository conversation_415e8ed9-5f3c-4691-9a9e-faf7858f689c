// 学科管理API测试套件
// 严格按照API契约进行测试，覆盖所有场景

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3001';
const API_ENDPOINT = '/api/subjects';

// 测试数据
const testSubject = {
  name: '测试学科_' + Date.now(),
  description: '这是一个测试学科描述'
};

const invalidSubjects = [
  { name: '', description: '空名称测试' },
  { name: 'a'.repeat(51), description: '超长名称测试' },
  { name: '数学', description: '重复名称测试' }
];

// 全局变量存储创建的学科ID
let createdSubjectId = null;

test.describe('学科管理API测试', () => {
  
  // 测试前准备：启动服务器
  test.beforeAll(async () => {
    console.log('🚀 开始学科管理API测试套件');
  });

  // 测试后清理
  test.afterAll(async () => {
    console.log('✅ 学科管理API测试套件完成');
  });

  test.describe('GET /api/subjects - 获取学科列表', () => {
    
    test('应该成功获取学科列表', async ({ request }) => {
      const response = await request.get(`${BASE_URL}${API_ENDPOINT}`);
      
      // 验证响应状态码
      expect(response.status()).toBe(200);
      
      // 验证响应格式
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('data');
      expect(Array.isArray(data.data)).toBe(true);
      
      // 验证数据结构
      if (data.data.length > 0) {
        const subject = data.data[0];
        expect(subject).toHaveProperty('id');
        expect(subject).toHaveProperty('name');
        expect(subject).toHaveProperty('created_at');
      }
      
      console.log(`📊 获取到 ${data.data.length} 个学科`);
    });

    test('应该按创建时间倒序排列', async ({ request }) => {
      const response = await request.get(`${BASE_URL}${API_ENDPOINT}`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      const subjects = data.data;
      
      if (subjects.length > 1) {
        for (let i = 0; i < subjects.length - 1; i++) {
          const current = new Date(subjects[i].created_at);
          const next = new Date(subjects[i + 1].created_at);
          expect(current.getTime()).toBeGreaterThanOrEqual(next.getTime());
        }
      }
    });
  });

  test.describe('POST /api/subjects - 创建学科', () => {
    
    test('应该成功创建新学科', async ({ request }) => {
      const response = await request.post(`${BASE_URL}${API_ENDPOINT}`, {
        data: testSubject
      });
      
      // 验证响应状态码
      expect(response.status()).toBe(201);
      
      // 验证响应格式
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('data');
      expect(data).toHaveProperty('message');
      
      // 验证创建的学科数据
      const subject = data.data;
      expect(subject).toHaveProperty('id');
      expect(subject.name).toBe(testSubject.name);
      expect(subject.description).toBe(testSubject.description);
      expect(subject).toHaveProperty('created_at');
      expect(subject).toHaveProperty('updated_at');
      
      // 保存创建的学科ID用于后续测试
      createdSubjectId = subject.id;
      
      console.log(`✅ 成功创建学科: ${subject.name} (ID: ${subject.id})`);
    });

    test('应该拒绝空名称', async ({ request }) => {
      const response = await request.post(`${BASE_URL}${API_ENDPOINT}`, {
        data: invalidSubjects[0]
      });
      
      expect(response.status()).toBe(400);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('message');
      expect(data.message).toContain('名称');
    });

    test('应该拒绝超长名称', async ({ request }) => {
      const response = await request.post(`${BASE_URL}${API_ENDPOINT}`, {
        data: invalidSubjects[1]
      });
      
      expect(response.status()).toBe(400);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('message');
    });

    test('应该拒绝重复名称', async ({ request }) => {
      const response = await request.post(`${BASE_URL}${API_ENDPOINT}`, {
        data: invalidSubjects[2]
      });
      
      expect(response.status()).toBe(409);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('message');
      expect(data.message).toContain('已存在');
    });

    test('应该处理缺少必填字段', async ({ request }) => {
      const response = await request.post(`${BASE_URL}${API_ENDPOINT}`, {
        data: { description: '只有描述没有名称' }
      });
      
      expect(response.status()).toBe(400);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
    });
  });

  test.describe('GET /api/subjects/:id - 获取学科详情', () => {
    
    test('应该成功获取存在的学科详情', async ({ request }) => {
      // 使用已知存在的学科ID（通常是1）
      const response = await request.get(`${BASE_URL}${API_ENDPOINT}/1`);
      
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('data');
      
      const subject = data.data;
      expect(subject).toHaveProperty('id', 1);
      expect(subject).toHaveProperty('name');
      expect(subject).toHaveProperty('created_at');
    });

    test('应该返回404对于不存在的学科', async ({ request }) => {
      const response = await request.get(`${BASE_URL}${API_ENDPOINT}/99999`);
      
      expect(response.status()).toBe(404);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('message');
    });

    test('应该返回400对于无效的ID格式', async ({ request }) => {
      const response = await request.get(`${BASE_URL}${API_ENDPOINT}/invalid`);
      
      expect(response.status()).toBe(400);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
    });
  });

  test.describe('性能测试', () => {
    
    test('API响应时间应该小于200ms', async ({ request }) => {
      const startTime = Date.now();
      
      const response = await request.get(`${BASE_URL}${API_ENDPOINT}`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status()).toBe(200);
      expect(responseTime).toBeLessThan(200);
      
      console.log(`⚡ API响应时间: ${responseTime}ms`);
    });
  });

  test.describe('错误处理测试', () => {
    
    test('应该处理无效的JSON数据', async ({ request }) => {
      const response = await request.post(`${BASE_URL}${API_ENDPOINT}`, {
        data: 'invalid json'
      });
      
      expect([400, 500]).toContain(response.status());
    });

    test('应该处理超大请求体', async ({ request }) => {
      const largeData = {
        name: 'a'.repeat(1000),
        description: 'b'.repeat(10000)
      };
      
      const response = await request.post(`${BASE_URL}${API_ENDPOINT}`, {
        data: largeData
      });
      
      expect(response.status()).toBe(400);
    });
  });
});
