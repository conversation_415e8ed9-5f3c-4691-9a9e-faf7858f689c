// 全局测试设置
// 在所有测试开始前启动服务器

const { spawn } = require('child_process');
const axios = require('axios');

let serverProcess = null;

async function globalSetup() {
  console.log('🚀 启动测试环境...');
  
  // 启动后端服务器
  serverProcess = spawn('node', ['app.js'], {
    cwd: __dirname + '/..',
    stdio: 'pipe',
    env: { ...process.env, NODE_ENV: 'test' }
  });

  // 监听服务器输出
  serverProcess.stdout.on('data', (data) => {
    console.log(`[Server] ${data.toString().trim()}`);
  });

  serverProcess.stderr.on('data', (data) => {
    console.error(`[Server Error] ${data.toString().trim()}`);
  });

  // 等待服务器启动
  await waitForServer('http://localhost:3001/health', 30000);
  
  console.log('✅ 测试环境启动完成');
  
  // 将进程ID保存到全局变量
  global.__SERVER_PROCESS__ = serverProcess;
}

async function waitForServer(url, timeout = 30000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await axios.get(url, { timeout: 5000 });
      if (response.status === 200) {
        console.log('✅ 服务器健康检查通过');
        return;
      }
    } catch (error) {
      // 继续等待
    }
    
    // 等待1秒后重试
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error(`服务器在 ${timeout}ms 内未能启动`);
}

module.exports = globalSetup;
