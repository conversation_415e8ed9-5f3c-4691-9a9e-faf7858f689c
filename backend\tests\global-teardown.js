// 全局测试清理
// 在所有测试结束后关闭服务器

async function globalTeardown() {
  console.log('🧹 清理测试环境...');
  
  // 获取服务器进程
  const serverProcess = global.__SERVER_PROCESS__;
  
  if (serverProcess && !serverProcess.killed) {
    console.log('🛑 关闭服务器进程...');
    
    // 优雅关闭
    serverProcess.kill('SIGTERM');
    
    // 等待进程结束
    await new Promise((resolve) => {
      serverProcess.on('exit', () => {
        console.log('✅ 服务器进程已关闭');
        resolve();
      });
      
      // 如果5秒内没有关闭，强制杀死
      setTimeout(() => {
        if (!serverProcess.killed) {
          console.log('⚠️ 强制关闭服务器进程');
          serverProcess.kill('SIGKILL');
        }
        resolve();
      }, 5000);
    });
  }
  
  console.log('✅ 测试环境清理完成');
}

module.exports = globalTeardown;
