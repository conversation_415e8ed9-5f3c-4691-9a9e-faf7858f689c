#!/usr/bin/env node

// E2E测试运行脚本
// 启动前后端服务，运行E2E测试，然后清理

import { spawn } from 'child_process'
import axios from 'axios'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 配置
const BACKEND_PORT = 3001
const FRONTEND_PORT = 3002
const BACKEND_URL = `http://localhost:${BACKEND_PORT}`
const FRONTEND_URL = `http://localhost:${FRONTEND_PORT}`

let backendProcess = null
let frontendProcess = null

// 解析命令行参数
const args = process.argv.slice(2)
const isHeaded = args.includes('--headed')
const isDebug = args.includes('--debug')
const browserArg = args.find(arg => arg.startsWith('--browser'))
const browser = browserArg ? browserArg.split('=')[1] : 'chromium'
const grepArg = args.find(arg => arg.startsWith('--grep'))
const grep = grepArg ? grepArg.split('=')[1] : null

console.log('🚀 启动E2E测试环境...')

// 启动后端服务
async function startBackend() {
  console.log('🔧 启动后端服务...')

  backendProcess = spawn('node', ['app.js'], {
    cwd: path.join(__dirname, '../../../backend'),
    stdio: 'pipe',
    env: { ...process.env, NODE_ENV: 'test' }
  })

  backendProcess.stdout.on('data', (data) => {
    if (isDebug) {
      console.log(`[Backend] ${data.toString().trim()}`)
    }
  })

  backendProcess.stderr.on('data', (data) => {
    console.error(`[Backend Error] ${data.toString().trim()}`)
  })

  // 等待后端启动
  await waitForService(BACKEND_URL + '/health', '后端服务')
}

// 启动前端服务
async function startFrontend() {
  console.log('🎨 启动前端服务...')

  frontendProcess = spawn('npm', ['run', 'dev'], {
    cwd: path.join(__dirname, '../..'),
    stdio: 'pipe',
    shell: true
  })

  frontendProcess.stdout.on('data', (data) => {
    if (isDebug) {
      console.log(`[Frontend] ${data.toString().trim()}`)
    }
  })

  frontendProcess.stderr.on('data', (data) => {
    if (isDebug) {
      console.error(`[Frontend Error] ${data.toString().trim()}`)
    }
  })

  // 等待前端启动
  await waitForService(FRONTEND_URL, '前端服务')
}

// 等待服务启动
async function waitForService(url, serviceName, timeout = 60000) {
  const startTime = Date.now()

  console.log(`⏳ 等待${serviceName}启动...`)

  while (Date.now() - startTime < timeout) {
    try {
      const response = await axios.get(url, { timeout: 5000 })
      if (response.status === 200) {
        console.log(`✅ ${serviceName}启动成功`)
        return
      }
    } catch (error) {
      // 继续等待
    }

    await new Promise(resolve => setTimeout(resolve, 2000))
  }

  throw new Error(`${serviceName}在 ${timeout}ms 内未能启动`)
}

// 运行E2E测试
async function runE2ETests() {
  console.log('🧪 运行E2E测试...')

  const playwrightArgs = [
    'test',
    'tests/e2e/',
    '--config=playwright.config.ts'
  ]

  if (isHeaded) {
    playwrightArgs.push('--headed')
  }

  if (browser && browser !== 'chromium') {
    playwrightArgs.push(`--project=${browser}`)
  }

  if (grep) {
    playwrightArgs.push(`--grep="${grep}"`)
  }

  // 添加报告输出
  playwrightArgs.push('--reporter=html,list')

  return new Promise((resolve, reject) => {
    const testProcess = spawn('npx', ['playwright', ...playwrightArgs], {
      cwd: path.join(__dirname, '../..'),
      stdio: 'inherit',
      shell: true
    })

    testProcess.on('exit', (code) => {
      if (code === 0) {
        console.log('✅ E2E测试完成')
        resolve()
      } else {
        console.error(`❌ E2E测试失败，退出码: ${code}`)
        reject(new Error(`E2E测试失败，退出码: ${code}`))
      }
    })

    testProcess.on('error', (error) => {
      console.error('❌ E2E测试进程错误:', error)
      reject(error)
    })
  })
}

// 清理进程
function cleanup() {
  console.log('🧹 清理测试环境...')

  if (backendProcess && !backendProcess.killed) {
    console.log('🛑 关闭后端服务...')
    backendProcess.kill('SIGTERM')
  }

  if (frontendProcess && !frontendProcess.killed) {
    console.log('🛑 关闭前端服务...')
    frontendProcess.kill('SIGTERM')
  }

  // 强制清理
  setTimeout(() => {
    if (backendProcess && !backendProcess.killed) {
      backendProcess.kill('SIGKILL')
    }
    if (frontendProcess && !frontendProcess.killed) {
      frontendProcess.kill('SIGKILL')
    }
  }, 5000)
}

// 主函数
async function main() {
  try {
    // 启动服务
    await startBackend()
    await startFrontend()

    // 运行测试
    await runE2ETests()

    console.log('🎉 E2E测试套件执行完成')

  } catch (error) {
    console.error('❌ E2E测试套件执行失败:', error.message)
    process.exit(1)
  } finally {
    cleanup()
  }
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n⚠️ 收到中断信号，正在清理...')
  cleanup()
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n⚠️ 收到终止信号，正在清理...')
  cleanup()
  process.exit(0)
})

// 运行主函数
main()
