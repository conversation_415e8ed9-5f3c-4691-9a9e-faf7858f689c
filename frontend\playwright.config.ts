import { defineConfig, devices } from '@playwright/test'

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests',
  /* 禁用并行测试以提高稳定性 */
  fullyParallel: false,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* 增加重试次数 */
  retries: process.env.CI ? 2 : 1,
  /* 使用单worker避免资源竞争 */
  workers: 1,
  /* 增加全局超时时间 */
  timeout: 60 * 1000, // 60秒
  /* 增加expect超时时间 */
  expect: {
    timeout: 15 * 1000, // 15秒
  },
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['list']
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:3002',
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',
    /* Record video on failure */
    video: 'retain-on-failure',
    /* 使用headless模式提高稳定性 */
    headless: true,
    /* 设置默认视口 */
    viewport: { width: 1280, height: 720 },
    /* 减少动画提高测试速度 */
    reducedMotion: 'reduce',
    /* 增加action超时 */
    actionTimeout: 20 * 1000, // 20秒
    /* 增加navigation超时 */
    navigationTimeout: 45 * 1000, // 45秒
  },

  /* Configure projects for major browsers - 只使用Chromium提高稳定性 */
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        /* 优化浏览器启动选项 */
        launchOptions: {
          timeout: 60 * 1000, // 60秒浏览器启动超时
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding'
          ]
        }
      },
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3002',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },

  /* Global setup and teardown - 暂时注释掉 */
  // globalSetup: './tests/global-setup.ts',
  // globalTeardown: './tests/global-teardown.ts',

  /* Output directory */
  outputDir: 'test-artifacts/',
})
